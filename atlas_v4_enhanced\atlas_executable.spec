# -*- mode: python ; coding: utf-8 -*-
"""
A.T.L.A.S. Trading System - PyInstaller Specification File
Creates a standalone executable with all dependencies, databases, and static files
"""

import os
import sys
from pathlib import Path

# Get the current directory (spec file context)
current_dir = Path(os.getcwd())

# Define all Python modules that need to be included
atlas_modules = [
    'atlas_server',
    'atlas_orchestrator', 
    'atlas_ai_core',
    'atlas_market_core',
    'atlas_trading_core',
    'atlas_risk_core',
    'atlas_database',
    'atlas_options',
    'atlas_education',
    'atlas_news_insights_engine',
    'atlas_lee_method',
    'atlas_realtime_scanner',
    'atlas_enhanced_market_data',
    'atlas_grok_integration',
    'atlas_utils',
    'atlas_progress_tracker',
    'atlas_conversation_monitor',
    'atlas_performance_monitor',
    'atlas_alert_manager',
    'atlas_causal_reasoning',
    'atlas_autonomous_agents',
    'atlas_theory_of_mind',
    'atlas_video_processor',
    'atlas_image_analyzer',
    'atlas_alternative_data',
    'atlas_data_fusion',
    'atlas_explainable_ai',
    'atlas_quantum_optimizer',
    'atlas_enhanced_intelligence_engine',
    'atlas_global_markets',
    'atlas_web_search_service',
    'atlas_input_validator',
    'atlas_math_safeguards',
    'atlas_ml_analytics',
    'atlas_monitoring',
    'atlas_rate_limiter',
    'atlas_realtime',
    'atlas_secrets_manager',
    'atlas_terminal_streamer',
    'config',
    'models',
    'sp500_symbols'
]

# Hidden imports for dependencies that PyInstaller might miss
hidden_imports = [
    # FastAPI and web framework
    'fastapi',
    'uvicorn',
    'uvicorn.workers',
    'uvicorn.protocols.http.auto',
    'uvicorn.protocols.websockets.auto',
    'uvicorn.lifespan.on',
    'starlette.middleware.cors',
    
    # Database
    'aiosqlite',
    'sqlalchemy',
    'sqlalchemy.dialects.sqlite',
    
    # AI and ML
    'openai',
    'httpx',
    'transformers',
    'torch',
    'tensorflow',
    'sklearn',
    'numpy',
    'pandas',
    'ta',
    
    # Trading APIs
    'alpaca_trade_api',
    'yfinance',
    
    # Async and networking
    'aiohttp',
    'asyncio',
    'websockets',
    
    # Configuration
    'pydantic',
    'pydantic_settings',
    'python_dotenv',
    
    # Utilities
    'psutil',
    'requests',
    'json',
    'datetime',
    'logging',
    'multiprocessing',
    'threading',
    
    # Optional ML libraries (with graceful fallbacks)
    'chromadb',
    'shap',
    'lime',
    'scipy',
    'statsmodels',
    'networkx',
    'cvxpy',
    'xgboost',
    
    # Computer vision and multimedia
    'cv2',
    'PIL',
    'librosa',
    
    # Web search
    'googleapiclient',
    'duckduckgo_search',
    'beautifulsoup4',
    'lxml',
    
    # Cryptocurrency
    'ccxt',
    'web3',
    
    # Additional dependencies
    'py_vollib',
    'tweepy',
    'SpeechRecognition'
]

# Data files to include (databases, HTML, config files)
datas = []

# Add files that exist
files_to_add = [
    ('atlas_interface.html', '.'),
    ('config_template.env', '.'),
    ('README.md', '.'),
    ('DEPLOYMENT_GUIDE.md', '.'),
    ('OPERATIONAL_GUIDE.md', '.'),
]

for src, dst in files_to_add:
    if os.path.exists(src):
        datas.append((src, dst))

# Add database files if they exist
if os.path.exists('databases'):
    for db_file in os.listdir('databases'):
        if db_file.endswith('.db'):
            datas.append((f'databases/{db_file}', 'databases'))

# Add static files if directory exists
if os.path.exists('static'):
    for static_file in os.listdir('static'):
        datas.append((f'static/{static_file}', 'static'))

# Binaries that might be needed
binaries = []

# Analysis configuration
a = Analysis(
    ['atlas_server.py'],  # Main entry point
    pathex=[str(current_dir)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # Exclude test files and development tools
        'pytest',
        'black',
        'flake8',
        'jupyter',
        'notebook',
        'ipython',
        'matplotlib.tests',
        'numpy.tests',
        'pandas.tests',
        'sklearn.tests',
        'tensorflow.python.debug',
        'tensorboard',
        'wandb',
        'mlflow'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Remove duplicate entries
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Create the executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ATLAS_Trading_System',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # Compress the executable
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Keep console for logging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
    version=None
)

# Optional: Create a directory distribution instead of single file
# Uncomment the following lines if you prefer a directory distribution
# coll = COLLECT(
#     exe,
#     a.binaries,
#     a.zipfiles,
#     a.datas,
#     strip=False,
#     upx=True,
#     upx_exclude=[],
#     name='ATLAS_Trading_System'
# )
