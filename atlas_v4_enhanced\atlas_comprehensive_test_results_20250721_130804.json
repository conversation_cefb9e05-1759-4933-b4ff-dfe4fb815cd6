{"category_1": [{"category": "Basic Functionality", "test_id": "1.1", "query": "What is the current price of AAPL stock?", "error": "HTTP 404", "response_time": 0.25725769996643066, "status": "ERROR", "timestamp": "2025-07-21T13:07:33.387250"}, {"category": "Basic Functionality", "test_id": "1.2", "query": "Provide the historical closing prices for TSLA over the last 7 days.", "error": "HTTP 404", "response_time": 0.002969980239868164, "status": "ERROR", "timestamp": "2025-07-21T13:07:35.402372"}, {"category": "Basic Functionality", "test_id": "1.3", "query": "What was the opening price of GOOGL on July 1, 2025?", "error": "HTTP 404", "response_time": 0.0037393569946289062, "status": "ERROR", "timestamp": "2025-07-21T13:07:37.420170"}, {"category": "Basic Functionality", "test_id": "1.4", "query": "List the top 5 gainers in the S&P 500 today.", "error": "HTTP 404", "response_time": 0.2714390754699707, "status": "ERROR", "timestamp": "2025-07-21T13:07:39.707100"}, {"category": "Basic Functionality", "test_id": "1.5", "query": "How has NVDA performed year-to-date?", "error": "HTTP 404", "response_time": 0.0020868778228759766, "status": "ERROR", "timestamp": "2025-07-21T13:07:41.718051"}], "category_2": [{"category": "Analytical/Algorithmic", "test_id": "2.1", "query": "Calculate the 50-day moving average for MSFT stock.", "error": "HTTP 404", "response_time": 0.26813626289367676, "status": "ERROR", "timestamp": "2025-07-21T13:07:43.987303"}, {"category": "Analytical/Algorithmic", "test_id": "2.2", "query": "Based on recent trends, is AMZN overbought or oversold? Explain using RSI.", "error": "HTTP 404", "response_time": 0.0031075477600097656, "status": "ERROR", "timestamp": "2025-07-21T13:07:46.010726"}, {"category": "Analytical/Algorithmic", "test_id": "2.3", "query": "Simulate a simple moving average crossover strategy for BTC-USD over the past month.", "error": "HTTP 404", "response_time": 0.003194570541381836, "status": "ERROR", "timestamp": "2025-07-21T13:07:48.018680"}, {"category": "Analytical/Algorithmic", "test_id": "2.4", "query": "What is the Sharpe ratio for a portfolio consisting of 50% AAPL and 50% META over last year?", "error": "HTTP 404", "response_time": 0.002167940139770508, "status": "ERROR", "timestamp": "2025-07-21T13:07:50.036135"}, {"category": "Analytical/Algorithmic", "test_id": "2.5", "query": "Predict the next day's price for SPY using a basic linear regression model on the past 30 days' data.", "error": "HTTP 404", "response_time": 0.00348663330078125, "status": "ERROR", "timestamp": "2025-07-21T13:07:52.053184"}], "category_3": [{"category": "Edge Cases/Error Handling", "test_id": "3.1", "query": "What is the stock price for a fictional company like 'XYZ Corp'?", "error": "HTTP 404", "response_time": 0.001253366470336914, "status": "ERROR", "timestamp": "2025-07-21T13:07:54.065250"}, {"category": "Edge Cases/Error Handling", "test_id": "3.2", "query": "Provide data for AAPL from February 30, 2025 (an invalid date).", "error": "HTTP 404", "response_time": 0.2537424564361572, "status": "ERROR", "timestamp": "2025-07-21T13:07:56.319430"}, {"category": "Edge Cases/Error Handling", "test_id": "3.3", "query": "How would the market react if interest rates drop to -5%? (Hypothetical extreme).", "error": "HTTP 404", "response_time": 0.004293680191040039, "status": "ERROR", "timestamp": "2025-07-21T13:07:58.335220"}, {"category": "Edge Cases/Error Handling", "test_id": "3.4", "query": "Get historical data for a delisted stock like ENRON.", "error": "HTTP 404", "response_time": 0.26931095123291016, "status": "ERROR", "timestamp": "2025-07-21T13:08:00.619112"}, {"category": "Edge Cases/Error Handling", "test_id": "3.5", "query": "What if I input a negative share quantity for a trading simulation?", "error": "HTTP 404", "response_time": 0.003545522689819336, "status": "ERROR", "timestamp": "2025-07-21T13:08:02.636432"}]}