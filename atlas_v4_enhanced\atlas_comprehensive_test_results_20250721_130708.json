{"category_1": [{"category": "Basic Functionality", "test_id": "1.1", "query": "What is the current price of AAPL stock?", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.2949752807617188, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:06.120170"}, {"category": "Basic Functionality", "test_id": "1.2", "query": "Provide the historical closing prices for TSLA over the last 7 days.", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.2752814292907715, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:10.409574"}, {"category": "Basic Functionality", "test_id": "1.3", "query": "What was the opening price of GOOGL on July 1, 2025?", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.3003299236297607, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:14.717464"}, {"category": "Basic Functionality", "test_id": "1.4", "query": "List the top 5 gainers in the S&P 500 today.", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.280076742172241, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:19.001103"}, {"category": "Basic Functionality", "test_id": "1.5", "query": "How has NVDA performed year-to-date?", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.2982168197631836, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:23.301004"}], "category_2": [{"category": "Analytical/Algorithmic", "test_id": "2.1", "query": "Calculate the 50-day moving average for MSFT stock.", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.284942626953125, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:27.588646"}, {"category": "Analytical/Algorithmic", "test_id": "2.2", "query": "Based on recent trends, is AMZN overbought or oversold? Explain using RSI.", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.2992656230926514, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:31.901791"}, {"category": "Analytical/Algorithmic", "test_id": "2.3", "query": "Simulate a simple moving average crossover strategy for BTC-USD over the past month.", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.287410020828247, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:36.199974"}, {"category": "Analytical/Algorithmic", "test_id": "2.4", "query": "What is the Sharpe ratio for a portfolio consisting of 50% AAPL and 50% META over last year?", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.2824456691741943, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:40.484067"}, {"category": "Analytical/Algorithmic", "test_id": "2.5", "query": "Predict the next day's price for SPY using a basic linear regression model on the past 30 days' data.", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.287583589553833, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:44.785188"}], "category_3": [{"category": "Edge Cases/Error Handling", "test_id": "3.1", "query": "What is the stock price for a fictional company like 'XYZ Corp'?", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.282149314880371, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:49.068442"}, {"category": "Edge Cases/Error Handling", "test_id": "3.2", "query": "Provide data for AAPL from February 30, 2025 (an invalid date).", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.287175416946411, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:53.371302"}, {"category": "Edge Cases/Error Handling", "test_id": "3.3", "query": "How would the market react if interest rates drop to -5%? (Hypothetical extreme).", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.2982590198516846, "status": "EXCEPTION", "timestamp": "2025-07-21T13:06:57.683189"}, {"category": "Edge Cases/Error Handling", "test_id": "3.4", "query": "Get historical data for a delisted stock like ENRON.", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.283073663711548, "status": "EXCEPTION", "timestamp": "2025-07-21T13:07:01.982662"}, {"category": "Edge Cases/Error Handling", "test_id": "3.5", "query": "What if I input a negative share quantity for a trading simulation?", "error": "Cannot connect to host localhost:8000 ssl:default [The remote computer refused the network connection]", "response_time": 2.284499168395996, "status": "EXCEPTION", "timestamp": "2025-07-21T13:07:06.269669"}]}